{"version": "2.0.0", "tasks": [{"label": "Build Alice2", "type": "shell", "command": "${workspaceFolder}/alice2/build.bat", "group": {"kind": "build", "isDefault": true}, "options": {"cwd": "${workspaceFolder}/alice2"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$msCompile"]}, {"label": "Run Alice2", "type": "shell", "command": "${workspaceFolder}/alice2/run.bat", "group": "test", "dependsOn": "Build Alice2", "options": {"cwd": "${workspaceFolder}/alice2"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "Build and Run Alice2", "dependsOrder": "sequence", "dependsOn": ["Build Alice2", "Run Alice2"], "group": {"kind": "build", "isDefault": false}}]}