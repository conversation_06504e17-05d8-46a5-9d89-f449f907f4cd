#include "Math.h"

namespace alice2 {

    // Math.cpp now serves as a placeholder for the unified math library
    // All implementations have been moved to their respective files:
    // - Vector.cpp: Vec3 implementations (currently header-only)
    // - Matrix.cpp: Mat4 implementations (currently header-only)
    // - Quaternion.cpp: Quaternion implementations
    // - MathUtils.h: Utility functions (header-only)

} // namespace alice2
